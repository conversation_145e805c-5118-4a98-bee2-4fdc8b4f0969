"use client";

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import MenuScreen from './gigHunt/screens/MenuScreen';
import CharacterSelectScreen from './gigHunt/screens/CharacterSelectScreen';
import CharacterInfoScreen from './gigHunt/screens/CharacterInfoScreen';
import GameplayScreen from './gigHunt/screens/GameplayScreen';

// Game states
const GAME_STATES = {
  MENU: 'menu',
  INSTRUCTIONS: 'instructions',
  CHARACTER_SELECT: 'character_select',
  CHARACTER_INFO: 'character_info',
  GAMEPLAY: 'gameplay',
  GAME_OVER: 'game_over'
};

const GigHuntGame = () => {
  const [gameState, setGameState] = useState(GAME_STATES.MENU);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [selectedCharacter, setSelectedCharacter] = useState(null);

  // Handle state transitions with smooth animations
  const changeGameState = async (newState) => {
    setIsTransitioning(true);
    
    // Wait for exit animation
    await new Promise(resolve => setTimeout(resolve, 300));
    
    setGameState(newState);
    setIsTransitioning(false);
  };

  // Handle menu actions
  const handleMenuAction = (action) => {
    switch (action) {
      case 'play':
        changeGameState(GAME_STATES.CHARACTER_SELECT);
        break;
      case 'instructions':
        changeGameState(GAME_STATES.INSTRUCTIONS);
        break;
      default:
        break;
    }
  };

  // Handle character selection
  const handleCharacterSelect = (character) => {
    setSelectedCharacter(character);
    changeGameState(GAME_STATES.CHARACTER_INFO);
  };

  // Handle starting game from character info
  const handleStartGame = () => {
    changeGameState(GAME_STATES.GAMEPLAY);
  };

  // Handle going back to character select
  const handleBackToCharacterSelect = () => {
    changeGameState(GAME_STATES.CHARACTER_SELECT);
  };

  // Handle going back to menu
  const handleBackToMenu = () => {
    changeGameState(GAME_STATES.MENU);
  };

  // Render current screen based on game state
  const renderCurrentScreen = () => {
    switch (gameState) {
      case GAME_STATES.MENU:
        return (
          <MenuScreen
            onMenuAction={handleMenuAction}
            isTransitioning={isTransitioning}
          />
        );

      case GAME_STATES.CHARACTER_SELECT:
        return (
          <CharacterSelectScreen
            onCharacterSelect={handleCharacterSelect}
            onBackToMenu={handleBackToMenu}
            isTransitioning={isTransitioning}
          />
        );

      case GAME_STATES.CHARACTER_INFO:
        return (
          <CharacterInfoScreen
            character={selectedCharacter}
            onStartGame={handleStartGame}
            onBackToSelect={handleBackToCharacterSelect}
            isTransitioning={isTransitioning}
          />
        );

      case GAME_STATES.GAMEPLAY:
        return (
          <GameplayScreen
            onBackToMenu={handleBackToMenu}
            isTransitioning={isTransitioning}
            selectedCharacter={selectedCharacter}
          />
        );
      
      default:
        return (
          <MenuScreen 
            onMenuAction={handleMenuAction}
            isTransitioning={isTransitioning}
          />
        );
    }
  };

  return (
    <div className="w-full h-full bg-black relative overflow-hidden">
      {/* Game Container with CRT effect */}
      <div className="w-full h-full relative">
        <AnimatePresence mode="wait">
          <motion.div
            key={gameState}
            initial={{ opacity: 0 }}
            animate={{ opacity: isTransitioning ? 0 : 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="w-full h-full"
          >
            {renderCurrentScreen()}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default GigHuntGame;
