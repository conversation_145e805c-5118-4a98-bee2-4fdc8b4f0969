"use client";

import Background from '../components/Background';

/**
 * MenuScreen - Main menu for Gig Hunt game
 * Shows title, menu options, and menu background
 */
const MenuScreen = ({ onMenuAction, isTransitioning }) => {
  
  const menuItems = [
    { id: 'play', label: 'PLAY', action: 'play' },
    { id: 'instructions', label: 'INSTRUCTIONS', action: 'instructions' }
  ];

  const handleMenuClick = (action) => {
    if (!isTransitioning) {
      onMenuAction(action);
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Background Layer */}
      <Background 
        backgroundType="menu"
        isTransitioning={isTransitioning}
      />

      {/* Menu Content Overlay */}
      <div className="relative z-50 w-full h-full game-content-layer flex items-start justify-center" style={{ paddingTop: '13%' }}>

        {/* Floating Menu Block - Scales with CRT screen size */}
        <div
          className="text-center"
          style={{
            width: '40%',  // 40% of CRT screen width
            height: 'auto', // Auto height
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '1.5rem' // Fixed gap between elements
          }}
        >
          {/* Game Title */}
          <div>
            <h1
              className="font-bold text-black tracking-wider"
              style={{
                fontFamily: 'VT323, monospace',
                fontSize: '5vw', // Since block is 40% width, this is 6% of block width
                lineHeight: '1',
                marginBottom: '0em'
              }}
            >
              GIG HUNT
            </h1>

            <p
              className="text-black tracking-wide"
              style={{
                fontFamily: 'VT323, monospace',
                fontSize: '1.2vw', // 2% of block width
                marginBottom: '1.5rem',
              }}
            >
              Hunt the Knowledge, Land the Gig
            </p>
          </div>

          {/* Menu Items */}
          <div
            className="flex flex-col text-center"
            style={{
              gap: '0.3vh' // Small gap between buttons
            }}
          >
            {menuItems.map((item) => (
              <div key={item.id}>
                <button
                  className="bg-transparent text-black hover:text-white cursor-pointer"
                  style={{
                    fontFamily: 'VT323, monospace',
                    fontSize: '1.5vw', // 3% of block width
                    letterSpacing: '2px',
                    border: 'none',
                    padding: '0',
                    margin: '0',
                    background: 'none',
                    display: 'inline'
                  }}
                  onClick={() => handleMenuClick(item.action)}
                  disabled={isTransitioning}
                >
                  {item.label}
                </button>
              </div>
            ))}
          </div>
        </div>

        {/* Subtitle/Credits */}
        <div className="absolute bottom-4 text-center">
          <p
            className="text-white tracking-wide"
            style={{
              fontFamily: 'VT323, monospace',
              fontSize: 'clamp(0.8rem, 1.5vw, 1.5rem)' // Scales smoothly from 0.8rem to 1.5rem
            }}
          >
            A Retro Experience
          </p>
        </div>
      </div>

      {/* Retro Scanlines Effect (Optional) */}
      <div 
        className="absolute inset-0 pointer-events-none z-40"
        style={{
          background: `repeating-linear-gradient(
            0deg,
            transparent,
            transparent 2px,
            rgba(0,0,0,0.1) 2px,
            rgba(0,0,0,0.1) 4px
          )`,
          opacity: 0.3
        }}
      />
    </div>
  );
};

export default MenuScreen;
