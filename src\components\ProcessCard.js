"use client";

import { motion } from 'framer-motion';

const ProcessCard = ({ processSteps, scrollProgress }) => {
  const cardCount = processSteps.length;
  const segmentSize = 1 / cardCount;

  // Card visibility - appears at scroll start, disappears when scrolling back up
  const cardVisible = scrollProgress > 0.05; // Appear when 5% into scroll

  // Debug logging
  console.log('Process scroll:', scrollProgress.toFixed(3), 'Card visible:', cardVisible);

  return (
    <div className="w-full h-full flex items-center justify-center">
      {/* Single card that appears/disappears with animation */}
      <motion.div
        className="w-[280px] h-[320px] bg-primary border-2 border-white/20 rounded-2xl shadow-lg relative overflow-hidden"
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{
          opacity: cardVisible ? 1 : 0,
          scale: cardVisible ? 1 : 0.8
        }}
        transition={{
          duration: 0.3,
          ease: "easeOut"
        }}
      >
        {/* Card Content - text fades based on scroll like project text */}
        {processSteps.map((step, index) => {
          // Calculate text opacity based on scroll progress (like project text)
          const segmentStart = index * segmentSize;
          const segmentEnd = (index + 1) * segmentSize;

          let textOpacity = 0;

          // Text is visible during its segment
          if (scrollProgress >= segmentStart && scrollProgress < segmentEnd) {
            textOpacity = 1;
          }

          // Handle last step completion
          if (scrollProgress >= 1.0 && index === cardCount - 1) {
            textOpacity = 1;
          }

          return (
            <motion.div
              key={step.id}
              className="absolute inset-0 p-6 flex flex-col items-center justify-center text-center"
              animate={{ opacity: textOpacity }}
              transition={{
                duration: 0.2,
                ease: "easeInOut"
              }}
            >
              {/* Step Number */}
              <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center shadow-md mb-4">
                <span className="text-primary text-xl font-bold font-heading">
                  {step.number}
                </span>
              </div>

              {/* Step Title */}
              <h3 className="text-secondary font-heading font-bold text-xl mb-4">
                {step.title}
              </h3>

              {/* Step Description */}
              <p className="text-secondary/80 text-sm leading-relaxed text-center">
                {step.description}
              </p>
            </motion.div>
          );
        })}
      </motion.div>
    </div>
  );
};

export default ProcessCard;
