"use client";

import { useState, useEffect, useRef } from 'react';

/**
 * SpriteAnimator - Handles frame-by-frame animation for individual SVG sprites
 * Perfect for retro-style hold keyframe animations (no smooth transitions)
 */
const SpriteAnimator = ({
  spriteBasePath,     // Base path to sprite folder
  frameNames,         // Array of frame filenames
  currentFrame = 0,   // Current frame index
  autoPlay = false,   // Auto-cycle through frames
  frameRate = 200,    // Milliseconds per frame (for autoPlay)
  loop = true,        // Loop animation when autoPlay reaches end
  className = "",     // Additional CSS classes
  style = {},         // Additional inline styles
  alt = "sprite",     // Alt text for accessibility
  onFrameChange,      // Callback when frame changes
  onAnimationEnd      // Callback when animation completes (non-looping)
}) => {
  const [internalFrame, setInternalFrame] = useState(0);
  const intervalRef = useRef(null);

  // Use external frame control or internal auto-play
  const activeFrame = autoPlay ? internalFrame : currentFrame;

  // Auto-play animation logic
  useEffect(() => {
    if (!autoPlay || !frameNames.length) return;

    intervalRef.current = setInterval(() => {
      setInternalFrame(prevFrame => {
        const nextFrame = prevFrame + 1;
        
        // Handle end of animation
        if (nextFrame >= frameNames.length) {
          if (loop) {
            return 0; // Loop back to start
          } else {
            // Stop at last frame and trigger callback
            if (onAnimationEnd) onAnimationEnd();
            return prevFrame;
          }
        }
        
        return nextFrame;
      });
    }, frameRate);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoPlay, frameNames.length, frameRate, loop, onAnimationEnd]);

  // Trigger frame change callback
  useEffect(() => {
    if (onFrameChange) {
      onFrameChange(activeFrame);
    }
  }, [activeFrame, onFrameChange]);

  // Safety check for frame bounds
  const safeFrame = Math.max(0, Math.min(activeFrame, frameNames.length - 1));
  const currentSpritePath = frameNames.length > 0 ? `${spriteBasePath}${frameNames[safeFrame]}` : '';

  if (!frameNames.length || !currentSpritePath) {
    return null;
  }

  return (
    <img
      src={currentSpritePath}
      alt={alt}
      className={`select-none ${className}`}
      style={{
        imageRendering: 'pixelated', // Maintain crisp pixel art
        ...style
      }}
      draggable={false}
    />
  );
};

export default SpriteAnimator;
