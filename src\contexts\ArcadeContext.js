"use client";

import React, { createContext, useContext, useState } from 'react';

// Create the Arcade Context
const ArcadeContext = createContext();

// Custom hook to use the Arcade context
export const useArcade = () => {
  const context = useContext(ArcadeContext);
  if (!context) {
    throw new Error('useArcade must be used within an ArcadeProvider');
  }
  return context;
};

// Arcade Provider component
export const ArcadeProvider = ({ children }) => {
  const [isArcadeActive, setIsArcadeActive] = useState(false);

  const enterArcade = () => {
    setIsArcadeActive(true);
    
    // Smooth scroll to retro games section after a brief delay
    setTimeout(() => {
      const retroSection = document.getElementById('retro-games-section');
      if (retroSection) {
        retroSection.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }, 100);
  };

  const exitArcade = () => {
    setIsArcadeActive(false);
    
    // Smooth scroll back to contact section
    setTimeout(() => {
      const contactSection = document.querySelector('[data-section="contact"]');
      if (contactSection) {
        contactSection.scrollIntoView({
          behavior: 'smooth',
          block: 'start'
        });
      }
    }, 100);
  };

  const value = {
    isArcadeActive,
    enterArcade,
    exitArcade
  };

  return (
    <ArcadeContext.Provider value={value}>
      {children}
    </ArcadeContext.Provider>
  );
};

export default ArcadeContext;
