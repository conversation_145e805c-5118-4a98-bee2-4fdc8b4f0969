"use client";

import React from 'react';
import { motion } from 'framer-motion';
import SnakeGame from '../RetroGames/games/SnakeGame';
import GigHuntGame from '../RetroGames/games/GigHuntGame';

const GameModal = ({ game }) => {
  if (!game) {
    return (
      <div className="p-8">
        <h2 className="text-2xl font-heading font-bold text-secondary mb-4">
          Game Modal
        </h2>
        <p className="text-secondary/80">
          No game data available.
        </p>
      </div>
    );
  }

  const renderGame = () => {
    switch (game.id) {
      case 'snake':
        return <SnakeGame />;
      case 'gighunt':
        return <GigHuntGame />;
      default:
        return (
          <div className="flex items-center justify-center h-full">
            <p className="text-secondary text-xl">Game not found</p>
          </div>
        );
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Game Header - positioned at top like project modal images */}
      <div className="mb-8">
        <div className="crt-screen active bg-black overflow-hidden shadow-lg" style={{ aspectRatio: '4/3' }}>
          {/* Game Content */}
          <div className="w-full h-full relative crt-content">
            {renderGame()}
          </div>
        </div>
      </div>

      {/* Game Info - positioned below the game */}
      <div className="p-8 pr-16 flex-1">
        <h1 className="text-3xl font-heading font-bold text-secondary mb-4 flex items-center gap-3">
          <span className="text-4xl">{game.icon}</span>
          {game.title}
        </h1>
        
        <p className="text-lg text-secondary/80 leading-relaxed mb-6">
          {game.description}
        </p>

        {/* Game Trivia */}
        {game.trivia && (
          <div className="mb-6">
            <h3 className="text-xl font-heading font-semibold text-secondary mb-3">
              Did You Know?
            </h3>
            <p className="text-secondary/80 leading-relaxed">
              {game.trivia}
            </p>
          </div>
        )}

        {/* Game Controls Info */}
        <div className="bg-background border border-secondary/20 rounded-lg p-4">
          <h4 className="font-semibold text-secondary mb-2">Controls:</h4>
          <div className="text-secondary/80 text-sm space-y-1">
            {game.id === 'snake' && (
              <>
                <p>• Arrow keys to move</p>
                <p>• Spacebar to start/restart</p>
                <p>• Avoid walls and yourself!</p>
              </>
            )}
            {game.id === 'gighunt' && (
              <>
                <p>• Click to shoot</p>
                <p>• Hunt software icons for knowledge</p>
                <p>• Then hunt projects to land the gig!</p>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GameModal;
