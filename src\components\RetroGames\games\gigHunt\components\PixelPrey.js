"use client";

import { useState, useEffect, useRef } from 'react';
import SpriteAnimator from './SpriteAnimator';

/**
 * PixelPrey - Individual flying software icon that can be shot
 * Handles flying animation, shot detection, and falling animation
 */
const PixelPrey = ({ 
  type, 
  startDelay = 0, 
  onShot, 
  onComplete,
  isGameActive = true 
}) => {
  const [preyState, setPreyState] = useState('emerging'); // emerging, flying, shot, falling
  const [currentFrame, setCurrentFrame] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const [shouldComplete, setShouldComplete] = useState(null); // null, { wasShot: boolean }
  const [currentPosition, setCurrentPosition] = useState({ x: 0, y: 0 });
  const [startPosition] = useState({
    x: Math.random() * 400 + 200, // Random position across grass area (200-600px from left)
    y: 400 + Math.random() * 50 // Start from grass level (around 400px from top + some variation)
  });

  // Use ref to track if animation should stop (for shot detection)
  const animationShouldStop = useRef(false);
  // Store the position where the prey was shot
  const shotPosition = useRef(null);

  // Available prey types with their sprite configurations
  const preyTypes = {
    illustrator: {
      basePath: '/Projects/Games/gh/pixelprey/illustrator/',
      flyFrames: ['illustrator_fly_01.svg', 'illustrator_fly_02.svg'],
      shotFrame: 'illustrator_shot.svg',
      fallFrame: 'illustrator_fly_01.svg', // Use fly frame 1 upside down for falling
      takesLife: false // Safe prey - gives points
    },
    ae: {
      basePath: '/Projects/Games/gh/pixelprey/ae/',
      flyFrames: ['ae_fly_01.svg', 'ae_fly_02.svg'],
      shotFrame: 'ae_shot.svg',
      fallFrame: 'ae_fly_01.svg',
      takesLife: false // Safe prey - gives points
    },
    blender: {
      basePath: '/Projects/Games/gh/pixelprey/blender/',
      flyFrames: ['blender_fly_01.svg', 'blender_fly_02.svg'], // Fixed typo: blener -> blender
      shotFrame: 'blender_shot.svg',
      fallFrame: 'blender_fly_01.svg',
      takesLife: false // Safe prey - gives points
    },
    netflix: {
      basePath: '/Projects/Games/gh/pixelprey/netflix/',
      flyFrames: ['nf_fly_01.svg', 'nf_fly_02.svg'],
      shotFrame: 'nf_shot.svg',
      fallFrame: 'nf_fly_01.svg',
      takesLife: true // Dangerous prey - takes life when shot
    },
    reddit: {
      basePath: '/Projects/Games/gh/pixelprey/reddit/',
      flyFrames: ['rd_fly_01.svg', 'rd_fly_02.svg'],
      shotFrame: 'rd_shot.svg',
      fallFrame: 'rd_fly_01.svg',
      takesLife: true // Dangerous prey - takes life when shot
    }
  };

  const currentPrey = preyTypes[type] || preyTypes.illustrator;

  // Handle completion callback in useEffect to avoid render cycle issues
  useEffect(() => {
    if (shouldComplete !== null && onComplete) {
      onComplete(type, shouldComplete.wasShot);
    }
  }, [shouldComplete, onComplete, type]);

  // Handle click/shot detection
  const handleClick = (e) => {
    e.stopPropagation();
    if (preyState === 'flying' && isGameActive) {
      console.log(`PixelPrey ${type}: Shot at position`, currentPosition);
      animationShouldStop.current = true; // Stop the flight animation
      shotPosition.current = { ...currentPosition }; // Capture the exact shot position
      setPreyState('shot');
      if (onShot) {
        onShot(type, currentPrey.takesLife); // Pass whether this prey takes a life
      }
    }
  };

  // Main animation sequence - using smooth movement
  useEffect(() => {
    if (!isGameActive) return;

    let timeoutId;
    let animationId;

    console.log(`PixelPrey ${type}: Starting with delay ${startDelay}ms at position`, startPosition);

    // Start delay
    timeoutId = setTimeout(() => {
      console.log(`PixelPrey ${type}: Becoming visible and emerging from grass`);
      setIsVisible(true);
      setPreyState('emerging');

      // Brief emergence phase, then start flying
      setTimeout(() => {
        console.log(`PixelPrey ${type}: Starting flight`);
        setPreyState('flying');
      }, 300); // 300ms emergence time

      // Smooth animation using requestAnimationFrame
      const startTime = Date.now();
      const flightDuration = 5000; // 5 seconds for longer flight
      const endPosition = {
        x: startPosition.x + (Math.random() * 600 - 300), // Fly in random direction (left or right) - wider range
        y: -100 + Math.random() * 50    // Fly off screen (above the top)
      };

      const animate = () => {
        // Check if animation should stop (prey was shot)
        if (animationShouldStop.current) {
          console.log(`PixelPrey ${type}: Animation stopped - prey was shot`);
          return;
        }

        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / flightDuration, 1);

        // Eased progress (ease-out)
        const easedProgress = 1 - Math.pow(1 - progress, 3);

        const newX = startPosition.x + (endPosition.x - startPosition.x) * easedProgress;
        const newY = startPosition.y + (endPosition.y - startPosition.y) * easedProgress;

        setCurrentPosition({ x: newX, y: newY });

        if (progress < 1 && !animationShouldStop.current) {
          animationId = requestAnimationFrame(animate);
        } else if (progress >= 1) {
          console.log(`PixelPrey ${type}: Flight complete`);
          setPreyState(currentState => {
            if (currentState === 'flying') {
              setShouldComplete({ wasShot: false });
              return 'complete';
            }
            return currentState;
          });
        }
      };

      animate();

    }, startDelay);

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (animationId) cancelAnimationFrame(animationId);
    };
  }, []); // Empty dependency array - run only once

  // Handle shot animation
  useEffect(() => {
    if (preyState === 'shot') {
      let animationId;
      let shotTimeout;

      console.log(`PixelPrey ${type}: Shot! Current position:`, currentPosition);

      // Brief shot frame display (prey stops immediately when shot)
      shotTimeout = setTimeout(() => {
        console.log(`PixelPrey ${type}: Starting fall from position`, currentPosition);
        setPreyState('falling');

        // CSS animation will handle the falling - just wait for it to complete
        setTimeout(() => {
          console.log(`PixelPrey ${type}: Fall complete - reached grass`);
          // Don't set to 'complete' immediately - let it stay in grass briefly
          setTimeout(() => {
            setPreyState('complete');
            setShouldComplete({ wasShot: true });
          }, 500); // Stay in grass for 500ms before completing
        }, 1500); // Match CSS animation duration

      }, 400); // 400ms shot frame display

      return () => {
        if (shotTimeout) clearTimeout(shotTimeout);
      };
    }
  }, [preyState, type]); // Removed currentPosition from dependencies to avoid re-triggering

  // Get current sprite frame
  const getCurrentSprite = () => {
    switch (preyState) {
      case 'shot':
        return [currentPrey.shotFrame];
      case 'falling':
        return [currentPrey.fallFrame];
      case 'flying':
      default:
        return currentPrey.flyFrames;
    }
  };

  if (!isVisible || preyState === 'complete') {
    return null;
  }

  // Debug logging
  console.log(`PixelPrey ${type}:`, {
    isVisible,
    preyState,
    currentSprite: getCurrentSprite(),
    basePath: currentPrey.basePath
  });

  return (
    <div
      className="absolute z-50" // Higher z-index for proper layering
      style={{
        left: `${currentPosition.x}px`,
        top: preyState === 'falling' ? '500px' : `${currentPosition.y}px`, // Fall all the way to bottom of screen
        // Apply grass mask when emerging from grass or falling back into it
        clipPath: (preyState === 'emerging' || preyState === 'falling') ? 'polygon(0 0, 100% 0, 100% 75%, 0 75%)' : 'none',
        transform: preyState === 'falling' ? 'rotate(180deg)' : 'none', // Only rotate, don't translate
        transition: preyState === 'falling' ? 'top 1.5s ease-in, transform 0.2s ease-out' : 'none' // Separate transitions
      }}
    >
      <div
        onClick={handleClick}
        className={`${preyState === 'flying' ? 'hover:scale-110' : ''}`}
        style={{ cursor: 'inherit' }} // Inherit the crosshair cursor from parent
      >
        <SpriteAnimator
          spriteBasePath={currentPrey.basePath}
          frameNames={getCurrentSprite()}
          autoPlay={preyState === 'flying'}
          frameRate={400} // Flying animation speed (slightly slower for smooth wing flapping)
          loop={preyState === 'flying'}
          className={`${preyState === 'shot' ? 'w-20 h-20' : 'w-16 h-16'}`} // Bigger when shot
          style={{
            objectFit: 'contain', // Keep aspect ratio and prevent stretching
            width: preyState === 'shot' ? '80px' : '64px',
            height: preyState === 'shot' ? '80px' : '64px'
          }}
          alt={`${type} pixelprey ${preyState}`}
        />
      </div>
    </div>
  );
};

export default PixelPrey;
