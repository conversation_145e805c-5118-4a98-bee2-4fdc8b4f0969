import { Open_Sans, Poppins, VT323 } from "next/font/google";
import "./globals.css";
import ClientThemeProvider from "../components/ClientThemeProvider";
import { ArcadeProvider } from "../contexts/ArcadeContext";

const openSans = Open_Sans({
  variable: "--font-open-sans",
  subsets: ["latin"],
  display: "swap",
});

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  display: "swap",
  weight: ["400", "700"], // Example weights, adjust as needed
});

const vt323 = VT323({
  variable: "--font-vt323",
  subsets: ["latin"],
  display: "swap",
  weight: ["400"], // VT323 only has one weight
});

export const metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${openSans.variable} ${poppins.variable} ${vt323.variable} antialiased`}
      >
        <ClientThemeProvider>
          <ArcadeProvider>
            {children}
          </ArcadeProvider>
        </ClientThemeProvider>
      </body>
    </html>
  );
}
