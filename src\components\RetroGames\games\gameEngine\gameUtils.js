// Game utility functions

// Generate random position within grid bounds
export const getRandomPosition = (gridWidth, gridHeight) => {
  return {
    x: Math.floor(Math.random() * gridWidth),
    y: Math.floor(Math.random() * gridHeight)
  };
};

// Check if two positions are equal
export const positionsEqual = (pos1, pos2) => {
  return pos1.x === pos2.x && pos1.y === pos2.y;
};

// Check if position is within bounds
export const isWithinBounds = (position, gridWidth, gridHeight) => {
  return position.x >= 0 && position.x < gridWidth && 
         position.y >= 0 && position.y < gridHeight;
};

// Check if position collides with any position in an array
export const checkCollision = (position, positions) => {
  return positions.some(pos => positionsEqual(position, pos));
};

// Direction vectors for movement
export const DIRECTIONS = {
  UP: { x: 0, y: -1 },
  DOWN: { x: 0, y: 1 },
  LEFT: { x: -1, y: 0 },
  RIGHT: { x: 1, y: 0 }
};

// Get opposite direction (for preventing reverse movement)
export const getOppositeDirection = (direction) => {
  const opposites = {
    UP: DIRECTIONS.DOWN,
    DOWN: DIRECTIONS.UP,
    LEFT: DIRECTIONS.RIGHT,
    RIGHT: DIRECTIONS.LEFT
  };
  
  // Find the direction key by comparing values
  const directionKey = Object.keys(DIRECTIONS).find(
    key => DIRECTIONS[key].x === direction.x && DIRECTIONS[key].y === direction.y
  );
  
  return opposites[directionKey] || direction;
};

// Add two positions together
export const addPositions = (pos1, pos2) => {
  return {
    x: pos1.x + pos2.x,
    y: pos1.y + pos2.y
  };
};

// Game state constants
export const GAME_STATES = {
  READY: 'ready',
  PLAYING: 'playing',
  PAUSED: 'paused',
  GAME_OVER: 'game_over'
};

// Color utilities for games
export const GAME_COLORS = {
  SNAKE_HEAD: '#4ade80',
  SNAKE_BODY: '#22c55e',
  FOOD: '#ef4444',
  BACKGROUND: '#000000',
  GRID: '#1a1a1a',
  TEXT: '#ffffff'
};
