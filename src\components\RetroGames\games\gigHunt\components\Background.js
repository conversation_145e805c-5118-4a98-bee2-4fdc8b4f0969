"use client";

/**
 * Background - Handles background elements for Gig Hunt
 * Simple static background with no transitions
 */
const Background = ({
  className = ""
}) => {
  
  const basePath = '/Projects/Games/gh/background/';
  
  // Use single background image for both menu and gameplay
  const backgroundImage = `${basePath}bg_image.svg`;

  return (
    <div className={`absolute inset-0 w-full h-full background-layer ${className}`}>
      {/* Single Background Image - no sliding transition */}
      <div className="relative w-full h-full">
        {/* Complete Background Image */}
        <div className="absolute inset-0 z-0">
          <img
            src={backgroundImage}
            alt="Game background"
            className="w-full h-full object-cover"
            style={{ imageRendering: 'pixelated' }}
          />
        </div>
      </div>
    </div>
  );
};

export default Background;
