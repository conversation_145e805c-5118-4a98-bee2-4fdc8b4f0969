"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const FAQContent = () => {
  const [openIndex, setOpenIndex] = useState(null);

  const faqData = [
    {
      question: "What services do you offer?",
      answer: "I specialize in multimedia design including poster design, branding, web development, and creative consulting. I work with both individual clients and businesses to create compelling visual solutions that communicate effectively with their target audience."
    },
    {
      question: "How long does a typical project take?",
      answer: "Project timelines vary depending on scope and complexity. Simple poster designs typically take 3-5 business days, while comprehensive branding projects can take 2-4 weeks. Web development projects usually range from 1-6 weeks depending on functionality requirements."
    },
    {
      question: "What is your design process?",
      answer: "My process begins with a discovery phase to understand your goals and target audience. I then create initial concepts, gather feedback, and refine the design through collaborative iterations. Finally, I deliver all necessary files and provide ongoing support as needed."
    },
    {
      question: "Do you offer revisions?",
      answer: "Yes! I include up to 3 rounds of revisions in all my projects to ensure the final result meets your expectations. Additional revisions can be accommodated if needed, and I'm always open to feedback throughout the creative process."
    },
    {
      question: "What file formats do you provide?",
      answer: "I deliver files in all necessary formats for your specific needs. This typically includes high-resolution print files (PDF, AI, EPS), web-optimized formats (PNG, JPG, SVG), and source files for future editing. I ensure you have everything needed for both digital and print applications."
    },
    {
      question: "How do you handle project pricing?",
      answer: "Pricing is based on project scope, complexity, and timeline. I provide detailed quotes after our initial consultation so you know exactly what to expect. I believe in transparent pricing with no hidden fees, and I'm happy to work within your budget constraints."
    },
    {
      question: "Do you work with international clients?",
      answer: "Absolutely! I work with clients worldwide and am comfortable collaborating across different time zones. All communication is handled digitally, and I use secure file sharing methods to ensure smooth project delivery regardless of location."
    },
    {
      question: "Can you help with ongoing design needs?",
      answer: "Yes, I love building long-term relationships with clients! Whether you need occasional design support or regular creative services, I can work with you to establish a retainer arrangement or project-based schedule that fits your needs."
    }
  ];

  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <div className="p-8 pr-16">
      <div className="mb-8">
        <h2 className="text-3xl font-heading font-bold text-secondary mb-4">
          Frequently Asked Questions
        </h2>
        <p className="text-secondary/80 text-lg">
          Find answers to common questions about my services, process, and working together.
        </p>
      </div>

      <div className="space-y-4">
        {faqData.map((faq, index) => (
          <div
            key={index}
            className="border border-secondary/20 rounded-lg overflow-hidden bg-background"
          >
            <button
              onClick={() => toggleFAQ(index)}
              className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-secondary/5 transition-colors duration-200 cursor-pointer"
            >
              <h3 className="font-semibold text-secondary text-lg pr-4">
                {faq.question}
              </h3>
              <motion.div
                animate={{ rotate: openIndex === index ? 180 : 0 }}
                transition={{ duration: 0.2, ease: "easeInOut" }}
                className="flex-shrink-0"
              >
                <svg
                  className="w-5 h-5 text-secondary/60"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                  strokeWidth={2}
                >
                  <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
                </svg>
              </motion.div>
            </button>
            
            <AnimatePresence>
              {openIndex === index && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.3, ease: "easeInOut" }}
                  className="overflow-hidden"
                >
                  <div className="px-6 pb-4 pt-0">
                    <p className="text-secondary/80 leading-relaxed">
                      {faq.answer}
                    </p>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </div>

      <div className="mt-8 p-6 bg-primary rounded-lg">
        <h3 className="font-semibold text-secondary text-lg mb-2">
          Still have questions?
        </h3>
        <p className="text-secondary/80 mb-4">
          Don't hesitate to reach out! I'm always happy to discuss your project and answer any specific questions you might have.
        </p>
        <a
          href="mailto:<EMAIL>"
          className="inline-flex items-center text-accent hover:text-accent/80 transition-colors duration-200 font-medium"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          Get in touch
        </a>
      </div>
    </div>
  );
};

export default FAQContent;
