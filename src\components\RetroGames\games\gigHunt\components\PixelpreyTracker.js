"use client";

import HUDContainer from './HUDContainer';

/**
 * PixelpreyTracker - Displays shot count for each pixelprey type with icons
 * Shows static pixelprey icons (no wings) with count numbers
 */
const PixelpreyTracker = ({ shotCounts = {} }) => {
  // Available safe pixelprey types (ducks) with their icon paths
  const preyTypes = [
    {
      type: 'illustrator',
      icon: '/Projects/Games/gh/pixelprey/illustrator/illustrator_pixelprey.svg',
      name: 'AI'
    },
    {
      type: 'ae',
      icon: '/Projects/Games/gh/pixelprey/ae/ae_pixelprey.svg',
      name: 'AE'
    },
    {
      type: 'blender',
      icon: '/Projects/Games/gh/pixelprey/blender/blender_pixelprey.svg',
      name: 'BL'
    }
  ];

  return (
    <HUDContainer position="bottom-right" padding="8px 12px">
      <div className="flex flex-col gap-2">
        <div className="flex items-center gap-3">
          {preyTypes.map((prey) => (
            <div key={prey.type} className="flex items-center gap-1">
              <img
                src={prey.icon}
                alt={`${prey.type} pixelprey`}
                className="w-5 h-5"
                style={{
                  imageRendering: 'pixelated', // Keep retro pixel look
                  filter: 'brightness(1.2)' // Slightly brighter for visibility
                }}
              />
              <span style={{ fontSize: '14px', minWidth: '16px' }}>
                {shotCounts[prey.type] || 0}
              </span>
            </div>
          ))}
        </div>
      </div>
    </HUDContainer>
  );
};

export default PixelpreyTracker;
