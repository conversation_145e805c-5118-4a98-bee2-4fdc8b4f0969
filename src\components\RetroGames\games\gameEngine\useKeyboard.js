"use client";

import { useEffect, useRef } from 'react';

const useKeyboard = (onKeyPress) => {
  const keysPressed = useRef(new Set());
  const lastKeyTime = useRef(0);
  const keyRepeatDelay = 150; // Milliseconds between key repeats

  useEffect(() => {
    const handleKeyDown = (event) => {
      const key = event.key;
      const now = Date.now();
      
      // Prevent default for arrow keys and space to avoid page scrolling
      if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' '].includes(key)) {
        event.preventDefault();
      }

      // Only process if key isn't already pressed or enough time has passed for repeat
      if (!keysPressed.current.has(key) || (now - lastKeyTime.current) > keyRepeatDelay) {
        keysPressed.current.add(key);
        lastKeyTime.current = now;
        onKeyPress(key);
      }
    };

    const handleKeyUp = (event) => {
      keysPressed.current.delete(event.key);
    };

    // Add event listeners
    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('keyup', handleKeyUp);

    // Cleanup
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('keyup', handleKeyUp);
    };
  }, [onKeyPress]);

  return keysPressed.current;
};

export default useKeyboard;
