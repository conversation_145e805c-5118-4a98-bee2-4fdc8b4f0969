"use client";

import { useState, useEffect, useRef } from 'react';
import Background from '../components/Background';
import SpriteAnimator from '../components/SpriteAnimator';
import PixelPreyManager from '../components/PixelPreyManager';
import LifeDisplay from '../components/LifeDisplay';
import HUDContainer from '../components/HUDContainer';
import PixelpreyTracker from '../components/PixelpreyTracker';

/**
 * GameplayScreen - Main gameplay screen for Gig Hunt
 * Handles the hunting gameplay, <PERSON>'s behavior, and PixelPrey spawning
 */
const GameplayScreen = ({ onBackToMenu, isTransitioning }) => {
  const [williamState, setWilliamState] = useState('idle');
  const [jumpFrame, setJumpFrame] = useState(1); // 1 or 2 for jump frames
  const [williamHidden, setWilliamHidden] = useState(false); // Track if <PERSON> is permanently hidden

  // Game state
  const [gameScore, setGameScore] = useState(0);
  const [preySpawningActive, setPreySpawningActive] = useState(false);
  const [gameComplete, setGameComplete] = useState(false);

  // Life system state
  const [lives, setLives] = useState(3);
  const [maxLives] = useState(3);
  const [isLosingLife, setIsLosingLife] = useState(false);
  const [gameOver, setGameOver] = useState(false);

  // Pixelprey tracking state
  const [shotCounts, setShotCounts] = useState({
    illustrator: 0,
    ae: 0,
    blender: 0,
    netflix: 0,
    reddit: 0
  });

  // Life loss visual effect state
  const [showLifeLossEffect, setShowLifeLossEffect] = useState(false);

  // Modal states
  const [showGameOverModal, setShowGameOverModal] = useState(false);
  const [showGameCompleteModal, setShowGameCompleteModal] = useState(false);
  const [finalGameStats, setFinalGameStats] = useState(null);

  // William position and animation state
  const [williamPosition, setWilliamPosition] = useState({ x: 0, y: 0 }); // Start at 0,0 relative to the -100px container
  const [isWilliamAnimating, setIsWilliamAnimating] = useState(false);
  const preyManagerRef = useRef(null);
  const isMountedRef = useRef(true);

  // William sprite configurations
  const williamSprites = {
    idle: {
      basePath: '/Projects/Games/gh/william/',
      frames: ['william_front_idle.svg'],
      frameRate: 1000 // Static frame
    },
    walking: {
      basePath: '/Projects/Games/gh/william/',
      frames: [
        'william_walk_right_01.svg',
        'william_walk_right_02.svg', 
        'william_walk_right_03.svg',
        'william_walk_right_04.svg'
      ],
      frameRate: 200
    },
    angry: {
      basePath: '/Projects/Games/gh/william/',
      frames: ['william_front_angry.svg'],
      frameRate: 1000
    },
    jumping: {
      basePath: '/Projects/Games/gh/william/',
      frames: [
        'william_jump_01.svg',
        'william_jump_02.svg'
      ],
      frameRate: 1000 // Static frame - no cycling
    }
  };

  // William animation sequence using CSS animations and timeouts
  useEffect(() => {
    const runGameSequence = () => {
      if (!isMountedRef.current) return;

      // Start walking immediately
      setWilliamState('walking');
      setWilliamPosition({ x: 0, y: 0 }); // Start from off-screen (container is at -100px)

      // Walk across screen - trigger CSS transition after a brief delay
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setIsWilliamAnimating(true);
        setWilliamPosition({ x: 500, y: 0 }); // Move to x: 500 (which is 400px from screen edge due to -100px container)
      }, 100);

      // After walking animation completes (3s + 100ms delay)
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setWilliamState('idle');
        setIsWilliamAnimating(false);
      }, 3100);

      // Idle, then get angry
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setWilliamState('angry');
      }, 4100);

      // Stay angry briefly, then jump
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setWilliamState('jumping');
        setJumpFrame(1);

        // Jump up - shorter transition for jump
        setIsWilliamAnimating(true);
        setWilliamPosition(prev => ({ ...prev, y: -160 }));
      }, 4900);

      // At peak: switch to frame 2 and apply mask
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setJumpFrame(2);
      }, 5300);

      // Fall down
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setWilliamPosition(prev => ({ ...prev, y: 0 }));
      }, 5600);

      // William is now permanently hidden behind grass
      setTimeout(() => {
        if (!isMountedRef.current) return;
        setWilliamHidden(true);
        setJumpFrame(1);
        setIsWilliamAnimating(false);
        setWilliamState('idle');

        // Start prey spawning after a short delay
        setTimeout(() => {
          if (!isMountedRef.current) return;
          setPreySpawningActive(true);
        }, 1500);
      }, 6200);
    };

    // Wait for transition to complete before starting game sequence
    if (isTransitioning) {
      const timer = setTimeout(() => {
        if (!isMountedRef.current) return;
        runGameSequence();
      }, 400); // Wait 400ms for transition to complete (300ms + buffer)

      return () => clearTimeout(timer);
    } else {
      // If not transitioning, start immediately
      runGameSequence();
    }

    // Cleanup function
    return () => {
      isMountedRef.current = false;
    };
  }, [isTransitioning]);

  // Cleanup effect to handle component unmounting
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
    };
  }, []);

  // Handle score updates from PixelPreyManager
  const handleScoreUpdate = ({ score, knowledge, preyType }) => {
    setGameScore(score);
    setGameKnowledge(knowledge);

    // Update shot count for this prey type (only for safe prey that give points)
    if (preyType) {
      setShotCounts(prev => ({
        ...prev,
        [preyType]: prev[preyType] + 1
      }));
    }
  };

  // Handle life loss from dangerous pixelpreys
  const handleLifeLost = (preyType) => {
    console.log('Life lost! Starting life loss animation...');
    setIsLosingLife(true);
    setShowLifeLossEffect(true);

    // Track dangerous prey shots too (even though they don't give points)
    if (preyType) {
      setShotCounts(prev => ({
        ...prev,
        [preyType]: prev[preyType] + 1
      }));
    }

    setLives(prev => {
      const newLives = prev - 1;
      console.log(`Lives remaining: ${newLives}`);

      if (newLives <= 0) {
        setGameOver(true);
        console.log('Game Over - No lives remaining!');
      }

      return newLives;
    });

    // Hide life loss effect after a short duration
    setTimeout(() => {
      setShowLifeLossEffect(false);
    }, 800); // 800ms flash effect
  };

  // Handle life loss animation completion
  const handleLifeLossComplete = () => {
    console.log('Life loss animation complete');
    setIsLosingLife(false);

    if (gameOver) {
      // Game over - show modal
      setShowGameOverModal(true);
    } else {
      // Resume game
      if (preyManagerRef.current) {
        preyManagerRef.current.resumeGame();
      }
    }
  };

  // Handle game completion
  const handleGameComplete = (gameStats) => {
    setGameComplete(true);
    setFinalGameStats(gameStats);
    console.log('Game Complete!', gameStats);
    // Show completion modal
    setShowGameCompleteModal(true);
  };

  const currentSprite = williamSprites[williamState];

  // Get the correct frame for jumping state
  const getCurrentFrame = () => {
    if (williamState === 'jumping') {
      return [currentSprite.frames[jumpFrame - 1]]; // Use specific jump frame
    }
    return currentSprite.frames;
  };

  return (
    <div
      className="relative w-full h-full overflow-hidden"
      style={{
        cursor: 'url(/Projects/Games/gh/background/crosshair.svg) 16 16, crosshair'
      }}
    >
      {/* Background Layer with smooth slide transition */}
      <Background
        backgroundType="gameplay"
        isTransitioning={isTransitioning}
      />

      {/* Game Content Layer with mask */}
      <div
        className="game-content-layer relative w-full h-full"
        style={{
          // Apply mask when William is in jump frame 2, and keep it on if William is hidden
          clipPath: (jumpFrame === 2 || williamHidden) ? 'polygon(0 0, 100% 0, 100% 75%, 0 75%)' : 'none'
        }}
      >
        {/* William Character */}
        <div className="absolute bottom-16 z-50" style={{ left: '-100px' }}>
          <div
            style={{
              transform: `translate(${williamPosition.x}px, ${williamPosition.y}px)`,
              opacity: isTransitioning ? 0 : 1,
              transition: williamState === 'walking' && isWilliamAnimating
                ? 'transform 3s linear'
                : williamState === 'jumping' && isWilliamAnimating
                ? 'transform 0.4s ease-out'
                : 'opacity 0.6s linear'
            }}
          >
            <SpriteAnimator
              spriteBasePath={currentSprite.basePath}
              frameNames={getCurrentFrame()}
              autoPlay={williamState !== 'jumping' && getCurrentFrame().length > 1}
              frameRate={currentSprite.frameRate}
              loop={williamState !== 'jumping'}
              className={`${williamState === 'jumping' && jumpFrame === 2 ? 'w-20 h-20' : 'w-24 h-24'}`}
              alt={`William ${williamState} ${williamState === 'jumping' ? `frame ${jumpFrame}` : ''}`}
            />
          </div>
        </div>

        {/* PixelPrey Manager - spawns and manages flying software icons */}
        <PixelPreyManager
          ref={preyManagerRef}
          isActive={preySpawningActive}
          onScoreUpdate={handleScoreUpdate}
          onGameComplete={handleGameComplete}
          onLifeLost={handleLifeLost}
        />

      </div>

      {/* Score Display - Bottom Left */}
      <HUDContainer position="bottom-left">
        <div>SCORE: {gameScore}</div>
        {gameComplete && (
          <div style={{ color: '#ffff00' }}>GAME COMPLETE!</div>
        )}
        {gameOver && (
          <div style={{ color: '#ff4444' }}>GAME OVER!</div>
        )}
      </HUDContainer>

      {/* Life Display - Top Right (Floating Hearts) */}
      <div className="absolute top-4 right-4 z-60">
        <LifeDisplay
          lives={lives}
          maxLives={maxLives}
          isLosingLife={isLosingLife}
          onLifeLost={handleLifeLossComplete}
        />
      </div>

      {/* Pixelprey Tracker - Bottom Right */}
      <PixelpreyTracker shotCounts={shotCounts} />

      {/* Back to Menu Button (Subtle) */}
      <div className="absolute top-4 left-4 z-60">
        <button
          onClick={onBackToMenu}
          className="text-black hover:text-gray-700 transition-colors opacity-70 hover:opacity-100"
          style={{
            fontFamily: 'var(--font-retro)',
            fontSize: '14px',
            textShadow: '1px 1px 0px #fff',
            background: 'none',
            border: 'none',
            cursor: 'pointer'
          }}
        >
          ← MENU
        </button>
      </div>

      {/* Life Loss Visual Effect */}
      {showLifeLossEffect && (
        <div
          className="absolute inset-0 z-70 pointer-events-none"
          style={{
            background: 'rgba(255, 255, 255, 0.8)',
            animation: 'lifeLossFlash 0.8s ease-out'
          }}
        />
      )}

      {/* Game Over Modal */}
      {showGameOverModal && (
        <div className="absolute inset-0 z-80 flex items-center justify-center bg-black bg-opacity-75">
          <div className="bg-black border-4 border-red-500 rounded-2xl p-8 text-center max-w-md">
            <h2
              className="text-red-500 text-4xl font-bold mb-4"
              style={{ fontFamily: 'VT323, monospace' }}
            >
              GAME OVER
            </h2>
            <p
              className="text-white text-xl mb-2"
              style={{ fontFamily: 'VT323, monospace' }}
            >
              FINAL SCORE: {gameScore}
            </p>
            <div className="flex justify-center gap-3 mb-6">
              {Object.entries(shotCounts).map(([preyType, count]) => (
                count > 0 && (
                  <div key={preyType} className="flex items-center gap-1">
                    <img
                      src={`/Projects/Games/gh/pixelprey/${preyType}/${preyType}_pixelprey.svg`}
                      alt={`${preyType} pixelprey`}
                      className="w-6 h-6"
                      style={{ imageRendering: 'pixelated' }}
                    />
                    <span
                      className="text-white text-lg"
                      style={{ fontFamily: 'VT323, monospace' }}
                    >
                      {count}
                    </span>
                  </div>
                )
              ))}
            </div>
            <button
              onClick={onBackToMenu}
              className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg text-lg font-bold transition-colors"
              style={{ fontFamily: 'VT323, monospace' }}
            >
              BACK TO MENU
            </button>
          </div>
        </div>
      )}

      {/* Game Complete Modal */}
      {showGameCompleteModal && (
        <div className="absolute inset-0 z-80 flex items-center justify-center bg-black bg-opacity-75">
          <div className="bg-black border-4 border-green-500 rounded-2xl p-8 text-center max-w-md">
            <h2
              className="text-green-500 text-4xl font-bold mb-4"
              style={{ fontFamily: 'VT323, monospace' }}
            >
              CONGRATULATIONS!
            </h2>
            <p
              className="text-white text-xl mb-2"
              style={{ fontFamily: 'VT323, monospace' }}
            >
              FINAL SCORE: {gameScore}
            </p>
            {finalGameStats && (
              <p
                className="text-white text-lg mb-4"
                style={{ fontFamily: 'VT323, monospace' }}
              >
                PREY HUNTED: {finalGameStats.hitPrey}/{finalGameStats.totalPrey}
              </p>
            )}
            <div className="flex justify-center gap-3 mb-6">
              {Object.entries(shotCounts).map(([preyType, count]) => (
                count > 0 && (
                  <div key={preyType} className="flex items-center gap-1">
                    <img
                      src={`/Projects/Games/gh/pixelprey/${preyType}/${preyType}_pixelprey.svg`}
                      alt={`${preyType} pixelprey`}
                      className="w-6 h-6"
                      style={{ imageRendering: 'pixelated' }}
                    />
                    <span
                      className="text-white text-lg"
                      style={{ fontFamily: 'VT323, monospace' }}
                    >
                      {count}
                    </span>
                  </div>
                )
              ))}
            </div>
            <button
              onClick={onBackToMenu}
              className="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg text-lg font-bold transition-colors"
              style={{ fontFamily: 'VT323, monospace' }}
            >
              BACK TO MENU
            </button>
          </div>
        </div>
      )}

      {/* CSS for life loss effect */}
      <style jsx>{`
        @keyframes lifeLossFlash {
          0% {
            background: rgba(255, 255, 255, 0.9);
            filter: saturate(0) brightness(2);
          }
          50% {
            background: rgba(255, 255, 255, 0.5);
            filter: saturate(0.3) brightness(1.5);
          }
          100% {
            background: rgba(255, 255, 255, 0);
            filter: saturate(1) brightness(1);
          }
        }
      `}</style>
    </div>
  );
};

export default GameplayScreen;
