"use client";

import { useState } from 'react';
import Background from '../components/Background';

/**
 * CharacterSelectScreen - Character selection for Gig Hunt game
 * Features multiple characters with selection state
 */
const CharacterSelectScreen = ({ onCharacterSelect, onBackToMenu, isTransitioning }) => {

  // State for selected character
  const [selectedCharacter, setSelectedCharacter] = useState(null);

  // Character data - expandable for future characters
  const characters = [
    {
      id: 'william',
      name: 'WILLIA<PERSON>',
      description: 'The Knowledge Hunter',
      spritePath: '/Projects/Games/gh/william/william_front_idle.svg',
      stats: {
        speed: 'FAST',
        accuracy: 'HIGH',
        knowledge: 'EXPERT'
      }
    },
    // Future characters can be added here
    // {
    //   id: 'character2',
    //   name: 'CHARACTER2',
    //   description: 'Description',
    //   spritePath: '/path/to/sprite.svg',
    //   stats: { speed: 'MEDIUM', accuracy: 'HIGH', knowledge: 'GOOD' }
    // }
  ];

  const handleCharacterClick = (character) => {
    if (!isTransitioning) {
      setSelectedCharacter(character);
    }
  };

  const handleSelectClick = () => {
    if (!isTransitioning && selectedCharacter) {
      onCharacterSelect(selectedCharacter);
    }
  };

  const handleBackClick = () => {
    if (!isTransitioning) {
      onBackToMenu();
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Background Layer */}
      <Background 
        backgroundType="menu"
        isTransitioning={isTransitioning}
      />

      {/* Back Button - Top Left Corner */}
      <div className="absolute top-8 left-8 z-50">
        <button
          className="bg-transparent text-black hover:text-white cursor-pointer"
          style={{
            fontFamily: 'VT323, monospace',
            fontSize: '1.5vw',
            letterSpacing: '2px',
            border: 'none',
            padding: '0',
            margin: '0',
            background: 'none',
            display: 'inline'
          }}
          onClick={handleBackClick}
          disabled={isTransitioning}
        >
          ← BACK
        </button>
      </div>

      {/* Character Selection Content */}
      <div className="relative z-50 w-full h-full game-content-layer flex items-start justify-center" style={{ paddingTop: '8%' }}>

        {/* Floating Character Select Block */}
        <div
          className="text-center"
          style={{
            width: '60%',
            height: 'auto',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '1.5rem'
          }}
        >
          {/* Screen Title */}
          <h1
            className="font-bold text-black tracking-wider"
            style={{
              fontFamily: 'VT323, monospace',
              fontSize: '3vw',
              marginBottom: '0em'
            }}
          >
            SELECT CHARACTER
          </h1>

          {/* Characters Grid */}
          <div style={{ display: 'flex', gap: '3rem', justifyContent: 'center', alignItems: 'center' }}>
            {characters.map((character) => (
              <div
                key={character.id}
                className="text-center cursor-pointer"
                onClick={() => handleCharacterClick(character)}
                style={{
                  opacity: selectedCharacter && selectedCharacter.id !== character.id ? 0.5 : 1
                }}
              >
                {/* Character Sprite */}
                <div style={{ marginBottom: '1rem' }}>
                  <img
                    src={character.spritePath}
                    alt={character.name}
                    style={{
                      imageRendering: 'pixelated',
                      filter: selectedCharacter?.id === character.id
                        ? 'drop-shadow(2px 2px 8px rgba(255,255,255,0.6))'
                        : 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))',
                      width: '6vw',
                      height: 'auto'
                    }}
                  />
                </div>

                {/* Character Name */}
                <h2
                  className="font-bold text-black"
                  style={{
                    fontFamily: 'VT323, monospace',
                    fontSize: '1.5vw',
                    marginBottom: '0rem'
                  }}
                >
                  {character.name}
                </h2>
              </div>
            ))}
          </div>

          {/* Select Button - Only show when character is selected */}
          {selectedCharacter && (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <button
                className="bg-transparent text-black hover:text-white cursor-pointer"
                style={{
                  fontFamily: 'VT323, monospace',
                  fontSize: '1.5vw',
                  letterSpacing: '2px',
                  border: 'none',
                  padding: '0',
                  margin: '0',
                  background: 'none',
                  display: 'inline'
                }}
                onClick={handleSelectClick}
                disabled={isTransitioning}
              >
                SELECT
              </button>
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="absolute bottom-4 text-center w-full">
          <p
            className="text-sm md:text-base lg:text-lg xl:text-xl text-white tracking-wide"
            style={{
              fontFamily: 'VT323, monospace'
            }}
          >
            {selectedCharacter ? 'Ready to Hunt?' : 'Choose Your Hunter'}
          </p>
        </div>
      </div>
    </div>
  );
};

export default CharacterSelectScreen;
