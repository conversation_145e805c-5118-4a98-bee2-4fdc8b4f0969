"use client";

import { useState, useRef, useEffect, useCallback } from 'react';
import useKeyboard from './gameEngine/useKeyboard';
import useGameLoop from './gameEngine/useGameLoop';
import { 
  getRandomPosition, 
  positionsEqual, 
  isWithinBounds, 
  checkCollision,
  DIRECTIONS,
  getOppositeDirection,
  addPositions,
  GAME_STATES,
  GAME_COLORS
} from './gameEngine/gameUtils';

const SnakeGame = () => {
  // Game configuration - Make it larger and properly proportioned
  const GRID_SIZE = 25; // Increased grid size for better gameplay
  const CANVAS_WIDTH = 800;  // Larger canvas to fill the CRT screen better
  const CANVAS_HEIGHT = 600; // 4:3 aspect ratio
  const CELL_SIZE = CANVAS_WIDTH / GRID_SIZE; // Proper cell size calculation
  
  // Game state
  const [gameState, setGameState] = useState(GAME_STATES.READY);
  const [score, setScore] = useState(0);
  const [snake, setSnake] = useState([{ x: 12, y: 12 }]);
  const [food, setFood] = useState({ x: 18, y: 8 });
  const [direction, setDirection] = useState(DIRECTIONS.RIGHT);
  
  const canvasRef = useRef(null);
  const gameStateRef = useRef(gameState);
  const snakeRef = useRef(snake);
  const foodRef = useRef(food);
  const directionRef = useRef(direction);

  // Keep refs in sync with state
  useEffect(() => { gameStateRef.current = gameState; }, [gameState]);
  useEffect(() => { snakeRef.current = snake; }, [snake]);
  useEffect(() => { foodRef.current = food; }, [food]);
  useEffect(() => { directionRef.current = direction; }, [direction]);

  // Generate new food position
  const generateFood = useCallback(() => {
    const gridHeight = Math.floor(CANVAS_HEIGHT / CELL_SIZE);
    let newFood;
    do {
      newFood = getRandomPosition(GRID_SIZE, gridHeight);
    } while (checkCollision(newFood, snakeRef.current));
    return newFood;
  }, []);

  // Game update function
  const updateGame = useCallback(() => {
    if (gameStateRef.current !== GAME_STATES.PLAYING) return;

    const currentSnake = [...snakeRef.current];
    const head = currentSnake[0];
    const newHead = addPositions(head, directionRef.current);

    // Calculate proper grid height for collision detection
    const gridHeight = Math.floor(CANVAS_HEIGHT / CELL_SIZE);

    // Check wall collision with correct bounds
    if (newHead.x < 0 || newHead.x >= GRID_SIZE || newHead.y < 0 || newHead.y >= gridHeight) {
      setGameState(GAME_STATES.GAME_OVER);
      return;
    }

    // Check self collision
    if (checkCollision(newHead, currentSnake)) {
      setGameState(GAME_STATES.GAME_OVER);
      return;
    }

    // Add new head
    currentSnake.unshift(newHead);

    // Check food collision
    if (positionsEqual(newHead, foodRef.current)) {
      setScore(prev => prev + 10);
      setFood(generateFood());
    } else {
      // Remove tail if no food eaten
      currentSnake.pop();
    }

    setSnake(currentSnake);
  }, [generateFood]);

  // Handle keyboard input
  const handleKeyPress = useCallback((key) => {
    if (gameStateRef.current === GAME_STATES.READY && key === ' ') {
      setGameState(GAME_STATES.PLAYING);
      return;
    }

    if (gameStateRef.current === GAME_STATES.GAME_OVER && key === ' ') {
      // Reset game
      setSnake([{ x: 12, y: 12 }]);
      setFood({ x: 18, y: 8 });
      setDirection(DIRECTIONS.RIGHT);
      setScore(0);
      setGameState(GAME_STATES.READY);
      return;
    }

    if (gameStateRef.current === GAME_STATES.PLAYING) {
      let newDirection = directionRef.current;

      switch (key) {
        case 'ArrowUp':
        case 'w':
        case 'W':
          newDirection = DIRECTIONS.UP;
          break;
        case 'ArrowDown':
        case 's':
        case 'S':
          newDirection = DIRECTIONS.DOWN;
          break;
        case 'ArrowLeft':
        case 'a':
        case 'A':
          newDirection = DIRECTIONS.LEFT;
          break;
        case 'ArrowRight':
        case 'd':
        case 'D':
          newDirection = DIRECTIONS.RIGHT;
          break;
        case ' ':
          setGameState(GAME_STATES.PAUSED);
          return;
      }

      // Prevent reverse direction
      const opposite = getOppositeDirection(directionRef.current);
      if (newDirection.x !== opposite.x || newDirection.y !== opposite.y) {
        setDirection(newDirection);
      }
    }

    if (gameStateRef.current === GAME_STATES.PAUSED && key === ' ') {
      setGameState(GAME_STATES.PLAYING);
    }
  }, []);

  // Set up keyboard and game loop
  useKeyboard(handleKeyPress);
  useGameLoop(updateGame, gameState === GAME_STATES.PLAYING, 8); // 8 FPS for classic feel

  // Canvas rendering
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    
    // Clear canvas
    ctx.fillStyle = GAME_COLORS.BACKGROUND;
    ctx.fillRect(0, 0, CANVAS_WIDTH, CANVAS_HEIGHT);

    // Draw grid (optional, subtle)
    ctx.strokeStyle = GAME_COLORS.GRID;
    ctx.lineWidth = 0.5;

    // Calculate grid dimensions properly
    const gridHeight = Math.floor(CANVAS_HEIGHT / CELL_SIZE);

    for (let x = 0; x <= GRID_SIZE; x++) {
      ctx.beginPath();
      ctx.moveTo(x * CELL_SIZE, 0);
      ctx.lineTo(x * CELL_SIZE, CANVAS_HEIGHT);
      ctx.stroke();
    }
    for (let y = 0; y <= gridHeight; y++) {
      ctx.beginPath();
      ctx.moveTo(0, y * CELL_SIZE);
      ctx.lineTo(CANVAS_WIDTH, y * CELL_SIZE);
      ctx.stroke();
    }

    // Draw snake
    snake.forEach((segment, index) => {
      ctx.fillStyle = index === 0 ? GAME_COLORS.SNAKE_HEAD : GAME_COLORS.SNAKE_BODY;
      ctx.fillRect(
        segment.x * CELL_SIZE + 1,
        segment.y * CELL_SIZE + 1,
        CELL_SIZE - 2,
        CELL_SIZE - 2
      );
    });

    // Draw food
    ctx.fillStyle = GAME_COLORS.FOOD;
    ctx.fillRect(
      food.x * CELL_SIZE + 2,
      food.y * CELL_SIZE + 2,
      CELL_SIZE - 4,
      CELL_SIZE - 4
    );

  }, [snake, food]);

  return (
    <div className="w-full h-full flex flex-col items-center justify-center p-4">
      {/* Game Info */}
      <div className="flex justify-between items-center w-full max-w-2xl mb-4">
        <div className="text-white font-mono">
          Score: {score}
        </div>
        <div className="text-white font-mono text-sm">
          {gameState === GAME_STATES.READY && "Press SPACE to start"}
          {gameState === GAME_STATES.PLAYING && "WASD or Arrow Keys to move"}
          {gameState === GAME_STATES.PAUSED && "Press SPACE to resume"}
          {gameState === GAME_STATES.GAME_OVER && "Press SPACE to restart"}
        </div>
      </div>

      {/* Game Canvas */}
      <canvas
        ref={canvasRef}
        width={CANVAS_WIDTH}
        height={CANVAS_HEIGHT}
        className="w-full h-full"
        style={{ imageRendering: 'pixelated' }}
      />

      {/* Game State Overlay */}
      {gameState !== GAME_STATES.PLAYING && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
          <div className="text-center text-white">
            {gameState === GAME_STATES.READY && (
              <>
                <h3 className="text-2xl font-bold mb-2">🐍 SNAKE</h3>
                <p className="mb-4">Eat the red food to grow!</p>
                <p className="text-sm opacity-75">Press SPACE to start</p>
              </>
            )}
            {gameState === GAME_STATES.PAUSED && (
              <>
                <h3 className="text-2xl font-bold mb-2">⏸️ PAUSED</h3>
                <p className="text-sm opacity-75">Press SPACE to resume</p>
              </>
            )}
            {gameState === GAME_STATES.GAME_OVER && (
              <>
                <h3 className="text-2xl font-bold mb-2">💀 GAME OVER</h3>
                <p className="mb-4">Final Score: {score}</p>
                <p className="text-sm opacity-75">Press SPACE to restart</p>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SnakeGame;
