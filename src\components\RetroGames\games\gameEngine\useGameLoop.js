"use client";

import { useEffect, useRef, useCallback } from 'react';

const useGameLoop = (gameUpdate, isRunning = true, targetFPS = 10) => {
  const animationFrameId = useRef();
  const lastUpdateTime = useRef(0);
  const frameInterval = 1000 / targetFPS; // Convert FPS to milliseconds

  const gameLoop = useCallback((currentTime) => {
    // Calculate time since last update
    const deltaTime = currentTime - lastUpdateTime.current;

    // Only update if enough time has passed (frame rate limiting)
    if (deltaTime >= frameInterval) {
      gameUpdate(deltaTime);
      lastUpdateTime.current = currentTime;
    }

    // Continue the loop if game is running
    if (isRunning) {
      animationFrameId.current = requestAnimationFrame(gameLoop);
    }
  }, [gameUpdate, isRunning, frameInterval]);

  useEffect(() => {
    if (isRunning) {
      lastUpdateTime.current = performance.now();
      animationFrameId.current = requestAnimationFrame(gameLoop);
    } else {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    }

    // Cleanup on unmount
    return () => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [isRunning, gameLoop]);

  // Return a function to manually stop the game loop
  const stopGameLoop = useCallback(() => {
    if (animationFrameId.current) {
      cancelAnimationFrame(animationFrameId.current);
    }
  }, []);

  return stopGameLoop;
};

export default useGameLoop;
