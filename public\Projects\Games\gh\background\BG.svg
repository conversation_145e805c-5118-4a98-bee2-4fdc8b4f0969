<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_2" data-name="Layer 2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 2046.51 1537.05">
  <defs>
    <style>
      .cls-1 {
        fill: url(#linear-gradient);
      }
    </style>
    <linearGradient id="linear-gradient" x1="1023.25" y1="-127.48" x2="1023.25" y2="1867.74" gradientUnits="userSpaceOnUse">
      <stop offset="0" stop-color="#00a6ff"/>
      <stop offset=".81" stop-color="#3bcaff"/>
      <stop offset="1" stop-color="#4ad4ff"/>
    </linearGradient>
  </defs>
  <g id="Layer_1-2" data-name="Layer 1">
    <rect class="cls-1" width="2046.51" height="1537.05"/>
  </g>
</svg>