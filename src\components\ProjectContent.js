"use client";

import { useModal } from '../contexts/ModalContext';

const ProjectContent = ({ project, projects, activeProjectIndex, opacity = 1, maskProgress = 0, blurAmount = 0 }) => {
  const { openModal } = useModal();

  const handleProjectClick = () => {
    openModal('project-details', project);
  };
  return (
    <div
      className="absolute inset-0 w-full h-full cursor-pointer bg-primary rounded-3xl overflow-hidden"
      style={{
        opacity: opacity,
        // Mask reveals from right to left
        clipPath: `inset(0 0 0 ${(1 - maskProgress) * 100}%)`,
      }}
      onClick={handleProjectClick}
    >
      {/* Image layer with blur effect */}
      {project.image ? (
        <img
          src={project.image}
          alt={project.title}
          className="w-full h-full object-cover"
          style={{
            // Blur effect during mask transition - only affects the image
            filter: blurAmount > 0 ? `blur(${blurAmount}px)` : 'none'
          }}
        />
      ) : (
        <div className="w-full h-full bg-primary/20 flex items-center justify-center">
          <span className="text-secondary text-lg font-medium">{project.title}</span>
        </div>
      )}
    </div>
  );
};

export default ProjectContent;
