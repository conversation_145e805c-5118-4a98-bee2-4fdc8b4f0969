"use client";

import Background from '../components/Background';

/**
 * CharacterInfoScreen - Shows detailed character information before gameplay
 */
const CharacterInfoScreen = ({ character, onStartGame, onBackToSelect, isTransitioning }) => {

  const handleStartGame = () => {
    if (!isTransitioning) {
      onStartGame();
    }
  };

  const handleBackClick = () => {
    if (!isTransitioning) {
      onBackToSelect();
    }
  };

  return (
    <div className="relative w-full h-full overflow-hidden">
      {/* Background Layer */}
      <Background 
        backgroundType="menu"
        isTransitioning={isTransitioning}
      />

      {/* Character Info Content - Constrained to top 70% */}
      <div className="relative z-50 w-full game-content-layer flex items-center justify-center" style={{ height: '70%' }}>

        {/* Split Screen Layout - Slightly Bigger */}
        <div
          style={{
            width: '75%',
            height: '80%',
            display: 'flex',
            alignItems: 'flex-start',
            gap: '0rem'
          }}
        >
          {/* Left Side - Character Image */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'flex-start'
            }}
          >
            <img
              src={character?.spritePath || '/Projects/Games/gh/william/william_front_idle.svg'}
              alt={character?.name || 'William'}
              style={{
                imageRendering: 'pixelated',
                filter: 'drop-shadow(2px 2px 4px rgba(0,0,0,0.3))',
                width: '10.5vw',
                height: 'auto',
                marginTop: '1rem',
              }}
            />
          </div>

          {/* Right Side - Character Info */}
          <div
            style={{
              flex: '1',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'flex-start',
              alignItems: 'flex-start',
              gap: '0rem'
            }}
          >
            {/* Character Name Title */}
            <h1
              className="font-bold text-black tracking-wider"
              style={{
                fontFamily: 'VT323, monospace',
                fontSize: '3.5vw',
                margin: '0',
                padding: '0',
                lineHeight: '1'
              }}
            >
              {character?.name || 'WILLIAM'}
            </h1>

            {/* Character Description */}
            <p
              className="text-black tracking-wide"
              style={{
                fontFamily: 'VT323, monospace',
                fontSize: '1.6vw',
                marginBottom: '0'
              }}
            >
              {character?.description || 'Long for Bill'}
            </p>

            {/* Character Stats */}
            <div style={{ marginBottom: '0.5rem' }}>
              {Object.entries(character?.stats || { speed: 'FAST', accuracy: 'HIGH', knowledge: 'EXPERT' }).map(([stat, value]) => (
                <div
                  key={stat}
                  className="text-black"
                  style={{
                    fontFamily: 'VT323, monospace',
                    fontSize: '1.3vw',
                    marginBottom: '0.4rem'
                  }}
                >
                  {stat.toUpperCase()}: {value}
                </div>
              ))}
            </div>

            {/* Action Buttons */}
            <div
              style={{
                
                display: 'flex',
                gap: '2rem',
                marginTop: '1.5rem',
              }}
            >
              {/* Back Button */}
              <button
                className="bg-transparent text-black hover:text-white cursor-pointer"
                style={{
                  fontFamily: 'VT323, monospace',
                  fontSize: '1.4vw',
                  letterSpacing: '2px',
                  border: 'none',
                  padding: '0',
                  margin: '0',
                  background: 'none',
                  display: 'inline'
                }}
                onClick={handleBackClick}
                disabled={isTransitioning}
              >
                ← BACK
              </button>

              {/* Start Game Button */}
              <button
                className="bg-transparent text-black hover:text-white cursor-pointer"
                style={{
                  fontFamily: 'VT323, monospace',
                  fontSize: '1.4vw',
                  letterSpacing: '2px',
                  border: 'none',
                  padding: '0',
                  margin: '0',
                  background: 'none',
                  display: 'inline'
                }}
                onClick={handleStartGame}
                disabled={isTransitioning}
              >
                START GAME →
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Instructions - Outside the constrained area */}
      <div className="absolute bottom-4 text-center w-full z-50">
        <p
          className="text-white tracking-wide"
          style={{
            fontFamily: 'VT323, monospace',
            fontSize: 'clamp(0.8rem, 1.5vw, 1.5rem)'
          }}
        >
          Ready to Hunt?
        </p>
      </div>
    </div>
  );
};

export default CharacterInfoScreen;
