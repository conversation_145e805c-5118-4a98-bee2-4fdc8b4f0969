"use client";

import { motion, AnimatePresence } from 'framer-motion';
import GameConsole from './GameConsole';
import Button from '../Button';
import { useArcade } from '../../contexts/ArcadeContext';

const RetroGamesSection = () => {
  const { isArcadeActive, enterArcade } = useArcade();

  // Available games
  const games = [
    {
      id: 'snake',
      title: 'Snake',
      description: 'Classic snake game where you eat food to grow longer while avoiding walls and yourself.',
      color: '#4ade80', // Green
      icon: '🐍',
      trivia: 'Originally created in 1976, <PERSON> became famous on Nokia phones in the late 90s. This version is built with React and Canvas API.'
    },
    {
      id: 'gighunt',
      title: 'Gig Hunt',
      description: 'Hunt software icons to gain knowledge points, then hunt projects to land the gig!',
      color: '#f59e0b', // Orange
      icon: '🎯',
      trivia: 'A creative twist on Duck Hunt! William helps you hunt software knowledge and projects. Built with React and SVG sprites for that authentic retro feel.'
    },
    // Future games will be added here
    // {
    //   id: 'tetris',
    //   title: 'Tetris',
    //   description: 'Block puzzle game where you arrange falling pieces to clear lines.',
    //   color: '#3b82f6', // Blue
    //   icon: '🧩',
    //   trivia: 'Created by <PERSON>ey Pajitnov in 1984, Tetris is one of the most recognizable puzzle games ever made. Coming soon to this arcade!'
    // },
    // {
    //   id: 'duckhunt',
    //   title: 'Duck Hunt',
    //   description: 'Shoot the flying ducks before they escape off screen.',
    //   color: '#f59e0b', // Orange
    //   icon: '🦆',
    //   trivia: 'Released by Nintendo in 1984, Duck Hunt used a light gun accessory. This web version uses mouse clicks instead!'
    // }
  ];

  // No longer needed since games open in modals

  return (
    <section
      id="retro-games-section"
      className="bg-background min-h-screen flex items-center justify-center relative z-10"
    >
      {/* Container with natural spacing */}
      <div className="w-3/4 mx-auto">
        <AnimatePresence mode="wait">
          {!isArcadeActive ? (
            // Entry Card State - Central card with enter button
            <motion.div
              key="entry-card"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="flex items-center justify-center"
              style={{ height: '70vh' }}
            >
              <div className="bg-primary rounded-3xl p-12 text-center max-w-2xl shadow-lg">
              <div className="space-y-6">
                <div className="flex items-center justify-center space-x-3 mb-6">
                  <span className="text-3xl animate-pulse">🎮</span>
                  <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl">
                    Retro Arcade
                  </h2>
                  <span className="text-3xl animate-pulse">🕹️</span>
                </div>

                <p className="text-secondary text-lg leading-relaxed mb-6">
                  Care for a nostalgia break? Check out some classic games recreated with modern web tech for your enjoyment.
                </p>

                <p className="text-secondary/80 text-base mb-8">
                  Want to see a more interactive project? Step into the arcade!
                </p>

                <Button
                  variant="filled"
                  onClick={enterArcade}
                >
                  Enter Arcade
                </Button>
              </div>
              </div>
            </motion.div>
          ) : (
            // Active Arcade State - Simplified layout with just game console
            <motion.div
              key="active-arcade"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="grid grid-cols-1 lg:grid-cols-3 gap-8"
              style={{ height: '80vh' }}
            >
          {/* Game Text - Left Side (1 column) */}
          <div className="lg:col-span-1 flex items-center bg-primary rounded-2xl p-8">
            <div className="w-full">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              >
                <h2 className="font-heading font-extrabold text-secondary text-3xl lg:text-4xl mb-8">
                  🎮 Retro Arcade
                </h2>
                <p className="text-secondary text-lg mb-8">
                  Take a nostalgia break! Classic games recreated with modern web tech for your enjoyment.
                </p>
                <div className="text-secondary/60 text-sm">
                  <p>Click on a game card to open it in a modal →</p>
                </div>
              </motion.div>
            </div>
          </div>

          {/* Game Area - Right Side (2 columns) - Simplified to just show console */}
          <div className="lg:col-span-2 flex items-center justify-center bg-background rounded-2xl p-6" style={{ height: '80vh' }}>
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.3, ease: [0.4, 0, 0.2, 1] }}
              className="w-full flex items-center justify-center"
            >
              <GameConsole
                games={games}
                onGameSelect={() => {}} // No longer needed since games open in modals
                isTransitioning={false}
              />
            </motion.div>
          </div>
        </motion.div>
          )}
        </AnimatePresence>
      </div>
    </section>
  );
};

export default RetroGamesSection;
