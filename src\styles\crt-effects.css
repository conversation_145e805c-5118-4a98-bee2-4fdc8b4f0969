/* CRT Screen Effects */

.crt-screen {
  position: relative;
  background: #000;
  
  /* Subtle curvature effect */
  transform: perspective(1000px) rotateX(0deg);
  
  /* Very subtle underlight with shadow */
  box-shadow:
    0 0 15px rgba(0, 255, 0, 0.03),
    0 0 30px rgba(0, 0, 0, 0.2),
    inset 0 0 100px rgba(0, 0, 0, 0.3);
}

.crt-screen::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10;
  pointer-events: none;
  
  /* Scanlines effect */
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 0, 0.03) 2px,
    rgba(0, 255, 0, 0.03) 4px
  );
  
  /* Subtle screen curvature using border-radius and transform */
  border-radius: inherit;
}

.crt-screen::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 15;
  pointer-events: none;
  
  /* Vignette effect for edge darkening - smaller and half opacity */
  background: radial-gradient(
    ellipse at center,
    transparent 75%,
    rgba(0, 0, 0, 0.05) 85%,
    rgba(0, 0, 0, 0.15) 100%
  );
  
  border-radius: inherit;
}

.crt-content {
  /* Slight barrel distortion effect */
  filter: contrast(1.1) brightness(1.05);

  /* Subtle screen flicker animation */
  animation: crt-flicker 0.15s infinite linear alternate;
}

/* Background should extend beyond rounded corners */
.crt-content .background-layer {
  /* Scale up background to fill rounded corners */
  transform: scale(1.1);
  transform-origin: center center;
}

/* Game content should be properly contained */
.crt-content .game-content-layer {
  /* Slight barrel distortion effect for game content only */
  transform: scale(0.98);
  transform-origin: center center;
}

@keyframes crt-flicker {
  0% {
    opacity: 1;
  }
  98% {
    opacity: 1;
  }
  99% {
    opacity: 0.98;
  }
  100% {
    opacity: 1;
  }
}

/* Enhanced CRT effect for when game is active */
.crt-screen.active {
  box-shadow:
    0 0 20px rgba(0, 255, 0, 0.05),
    0 0 40px rgba(0, 0, 0, 0.3),
    inset 0 0 100px rgba(0, 0, 0, 0.2);
}

.crt-screen.active::before {
  background: repeating-linear-gradient(
    0deg,
    transparent,
    transparent 2px,
    rgba(0, 255, 0, 0.05) 2px,
    rgba(0, 255, 0, 0.05) 4px
  );
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .crt-screen {
    /* Reduce effects on mobile for performance */
    box-shadow:
      0 0 15px rgba(0, 0, 0, 0.3),
      inset 0 0 50px rgba(0, 0, 0, 0.2);
  }
  
  .crt-screen::before {
    background: repeating-linear-gradient(
      0deg,
      transparent,
      transparent 3px,
      rgba(0, 255, 0, 0.02) 3px,
      rgba(0, 255, 0, 0.02) 6px
    );
  }
  
  .crt-content {
    animation: none; /* Disable flicker on mobile */
  }
}
