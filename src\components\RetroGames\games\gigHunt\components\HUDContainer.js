"use client";

/**
 * HUDContainer - Reusable styled container for HUD elements
 * Creates rounded rectangles with retro game styling
 */
const HUDContainer = ({ 
  children, 
  className = "",
  position = "top-left", // top-left, top-right, bottom-left, bottom-right
  padding = "12px 16px"
}) => {
  // Position classes mapping
  const positionClasses = {
    "top-left": "top-4 left-4",
    "top-right": "top-4 right-4", 
    "bottom-left": "bottom-4 left-4",
    "bottom-right": "bottom-4 right-4"
  };

  return (
    <div 
      className={`absolute z-60 ${positionClasses[position]} ${className}`}
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.8)', // Semi-transparent black
        border: '2px solid #00ff41', // Retro green border
        borderRadius: '12px', // 50% rounded corners
        padding: padding,
        backdropFilter: 'blur(4px)', // Subtle blur effect
        boxShadow: '0 0 10px rgba(0, 255, 65, 0.3)' // Subtle green glow
      }}
    >
      <div
        style={{
          fontFamily: 'var(--font-retro)', // VT323 font
          color: '#ffffff', // White text for better readability
          textShadow: '1px 1px 0px #000', // Black text shadow for readability
          fontSize: '16px',
          lineHeight: '1.2'
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default HUDContainer;
